* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root {
  --yellow: #FFD600;
  --yellow-dark: #FFC300;
  --black: #181818;
  --black-light: #232323;
  --white: #fff;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--black), var(--black-light));
  position: relative;
  overflow: hidden;
}

.app::before {
  content: '';
  position: absolute;
  left: -20vw;
  top: -20vw;
  width: 60vw;
  height: 60vw;
  background: radial-gradient(circle, #ffd60033 0%, transparent 70%);
  filter: blur(40px);
  z-index: 0;
  animation: bgmove 12s linear infinite alternate;
}

@keyframes bgmove {
  0% { left: -20vw; top: -20vw; }
  100% { left: 40vw; top: 30vw; }
}

.container {
  flex: 1;
  width: 100%;
  max-width: 800px;
  background: rgba(24, 24, 24, 0.95);
  backdrop-filter: blur(6px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.7);
  position: relative;
  z-index: 1;
  margin: 20px auto;
}

.search-box {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.search-box input {
  flex: 1;
  padding: 15px 20px;
  border: none;
  border-radius: 10px;
  background: var(--black-light);
  color: var(--yellow);
  font-size: 16px;
  transition: all 0.3s ease;
}

.search-box input::placeholder {
  color: #FFD60099;
}

.search-box input:focus {
  outline: none;
  background: #222;
}

.search-box button {
  padding: 15px 25px;
  border: none;
  border-radius: 10px;
  background: var(--yellow);
  color: var(--black);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px #ffd60033;
}

.search-box button:hover {
  background: var(--yellow-dark);
  transform: scale(1.05);
}

.weather-box {
  text-align: center;
  color: var(--yellow);
  margin-bottom: 30px;
}

.weather-info {
  margin-bottom: 20px;
}

.city {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--yellow);
  letter-spacing: 1px;
}

.date {
  font-size: 16px;
  opacity: 0.8;
  margin-bottom: 15px;
  color: var(--white);
}

.temperature {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--yellow);
  text-shadow: 0 2px 8px #ffd60033;
}

.weather-icon {
  font-size: 48px;
  margin-bottom: 15px;
  color: var(--yellow);
  filter: drop-shadow(0 2px 8px #ffd60055);
}

.description {
  font-size: 20px;
  text-transform: capitalize;
  color: var(--white);
}

.weather-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 1rem;
  backdrop-filter: blur(10px);
}

.detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 0.8rem;
  transition: all 0.3s ease;
}

.detail-item:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.detail-item i {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--blue-light);
}

.detail-item span {
  font-size: 0.9rem;
  color: var(--white);
  opacity: 0.8;
  margin-top: 0.3rem;
}

.detail-item .value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--white);
  margin: 0.3rem 0;
}

.forecast-container {
  margin-top: 30px;
}

.forecast-container h3 {
  color: var(--yellow);
  margin-bottom: 15px;
  font-size: 20px;
  letter-spacing: 1px;
}

.forecast {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.forecast-item {
  background: #232323;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  color: var(--yellow);
  box-shadow: 0 2px 8px #ffd60022;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 1;
}

.forecast-item:hover {
  transform: translateY(-5px) scale(1.04);
  box-shadow: 0 4px 16px #ffd60055;
}

.forecast-item .day {
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--white);
}

.forecast-item .temp {
  font-size: 18px;
  margin: 5px 0;
  color: var(--yellow);
}

.forecast-item i {
  font-size: 24px;
  margin: 5px 0;
  color: var(--yellow);
}

.loading {
  text-align: center;
  color: var(--yellow);
  font-size: 24px;
  padding: 20px;
}

.error {
  text-align: center;
  color: #ff6b6b;
  padding: 20px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 10px;
  margin: 20px 0;
}

.uvindex {
  display: none;
}

.hourly-container {
  margin-top: 30px;
  background: #181818cc;
  border-radius: 15px;
  padding: 20px 10px 10px 10px;
  box-shadow: 0 2px 16px #ffd60022;
  animation: fadeIn 0.7s;
}

.hourly-container h4 {
  color: var(--yellow);
  margin-bottom: 15px;
  font-size: 18px;
  letter-spacing: 1px;
  text-align: center;
}

.hourly-forecast {
  display: flex;
  overflow-x: auto;
  gap: 12px;
  padding: 10px 5px;
  scrollbar-width: thin;
  scrollbar-color: var(--yellow) #232323;
}

.hourly-forecast::-webkit-scrollbar {
  height: 6px;
}

.hourly-forecast::-webkit-scrollbar-track {
  background: #232323;
  border-radius: 3px;
}

.hourly-forecast::-webkit-scrollbar-thumb {
  background: var(--yellow);
  border-radius: 3px;
}

.hourly-item {
  min-width: 90px;
  background: #232323;
  border-radius: 10px;
  text-align: center;
  color: var(--yellow);
  box-shadow: 0 2px 8px #ffd60022;
  padding: 10px 6px;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeIn 0.5s;
  flex-shrink: 0;
}

.hourly-item:hover {
  transform: scale(1.08) translateY(-4px);
  box-shadow: 0 4px 16px #ffd60099;
  z-index: 2;
  background: linear-gradient(135deg, #232323 80%, #ffd60022 100%);
}

.hourly-item .hour {
  font-size: 15px;
  color: var(--white);
  margin-bottom: 4px;
  font-weight: 500;
}

.hourly-item i {
  font-size: 22px;
  margin: 4px 0;
  color: var(--yellow);
  filter: drop-shadow(0 0 6px #ffd60099);
}

.hourly-item .temp {
  font-size: 16px;
  color: var(--yellow);
  font-weight: 600;
  margin: 4px 0;
}

.hourly-item .desc {
  font-size: 12px;
  color: var(--white);
  opacity: 0.8;
  margin-top: 2px;
  text-transform: capitalize;
}

.forecast-item.selected {
  border: 2px solid var(--yellow);
  box-shadow: 0 0 16px #ffd600cc, 0 4px 16px #ffd60055;
  background: linear-gradient(135deg, #232323 80%, #ffd60022 100%);
  transform: scale(1.06);
  z-index: 2;
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@media (max-width: 600px) {
  .container {
    padding: 15px;
  }
  
  .weather-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    padding: 0.8rem;
  }
  
  .forecast {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }

  .hourly-container {
    margin-top: 20px;
    padding: 15px 5px 5px 5px;
  }

  .hourly-item {
    min-width: 80px;
    padding: 8px 4px;
  }

  .hourly-item .hour {
    font-size: 13px;
  }

  .hourly-item i {
    font-size: 20px;
  }

  .hourly-item .temp {
    font-size: 14px;
  }

  .hourly-item .desc {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .weather-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    padding: 0.8rem;
  }

  .detail-item {
    padding: 0.8rem;
  }

  .detail-item i {
    font-size: 1.3rem;
  }

  .detail-item .value {
    font-size: 1.1rem;
  }

  .detail-item span {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .weather-details {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .detail-item {
    padding: 0.6rem;
  }
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.app-header .app-title {
  margin: 0;
  font-size: 2.5rem;
  background: linear-gradient(45deg, var(--yellow), #fff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: none;
}

.app-header svg {
  filter: drop-shadow(0 2px 8px rgba(255, 214, 0, 0.3));
  transition: transform 0.3s ease;
}

.app-header:hover svg {
  transform: rotate(15deg);
}

.footer {
  width: 100%;
  background: rgba(24, 24, 24, 0.95);
  backdrop-filter: blur(10px);
  padding: 2rem 0 1rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 0 2rem;
}

.footer-section h3 {
  color: var(--yellow);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(255, 214, 0, 0.2);
}

.footer-section h4 {
  color: var(--yellow);
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.footer-section p {
  color: var(--white);
  opacity: 0.8;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section ul li {
  margin-bottom: 0.8rem;
}

.footer-section ul li a {
  color: var(--white);
  text-decoration: none;
  opacity: 0.8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.footer-section ul li a:hover {
  opacity: 1;
  color: var(--yellow);
  transform: translateX(5px);
}

.footer-section ul li a i {
  font-size: 1.1rem;
}

.footer-bottom {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 2rem;
}

.footer-bottom p {
  margin: 0.5rem 0;
  color: rgba(255, 255, 255, 0.7);
}

.developer-credit {
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem !important;
}

.developer-credit i {
  color: var(--yellow);
  font-size: 1rem;
}

.developer-credit .fa-heart {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.developer-credit a {
  color: var(--yellow);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 0.2rem;
}

.developer-credit a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--yellow);
  transition: width 0.3s ease;
}

.developer-credit a:hover {
  color: var(--yellow);
  transform: translateY(-2px);
}

.developer-credit a:hover::after {
  width: 100%;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 0 1.5rem;
  }
}

@media (max-width: 480px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .footer-section {
    text-align: center;
  }

  .footer-section ul li a {
    justify-content: center;
  }
}
