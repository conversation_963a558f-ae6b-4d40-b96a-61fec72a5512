<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/logo.svg" type="image/svg+xml" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="TasoWeather - Modern Hava Durumu Uygulaması"
    />
    
    <!-- iOS meta tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="TasoWeather" />
    
    <!-- iOS icons -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/app-icon.svg" />
    <link rel="apple-touch-icon" sizes="152x152" href="%PUBLIC_URL%/app-icon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/app-icon.svg" />
    <link rel="apple-touch-icon" sizes="167x167" href="%PUBLIC_URL%/app-icon.svg" />
    
    <!-- iOS splash screens -->
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash640x1136.png" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" />
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash750x1334.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" />
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash1242x2208.png" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" />
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash1125x2436.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" />
    <link rel="apple-touch-startup-image" href="%PUBLIC_URL%/splash1242x2688.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" />
    
    <!-- PWA meta tags -->
    <meta name="application-name" content="TasoWeather" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="msapplication-TileImage" content="%PUBLIC_URL%/app-icon.svg" />
    <meta name="msapplication-config" content="%PUBLIC_URL%/browserconfig.xml" />
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="TasoWeather - Modern Hava Durumu" />
    <meta property="og:description" content="Modern ve kullanıcı dostu hava durumu uygulaması" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://taso-weather.com" />
    <meta property="og:image" content="%PUBLIC_URL%/app-icon.svg" />
    
    <!-- Twitter meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="TasoWeather - Modern Hava Durumu" />
    <meta name="twitter:description" content="Modern ve kullanıcı dostu hava durumu uygulaması" />
    <meta name="twitter:image" content="%PUBLIC_URL%/app-icon.svg" />
    
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <title>TasoWeather™ | Modern Hava Durumu</title>
    
    <!-- iOS web app specific styles -->
    <style>
      @media all and (display-mode: standalone) {
        body {
          padding-top: env(safe-area-inset-top);
          padding-bottom: env(safe-area-inset-bottom);
          padding-left: env(safe-area-inset-left);
          padding-right: env(safe-area-inset-right);
        }
      }
    </style>
  </head>
  <body>
    <noscript>Bu uygulamayı çalıştırmak için JavaScript'i etkinleştirmeniz gerekiyor.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
