import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import axios from 'axios';
import { motion, AnimatePresence } from 'framer-motion';
import '@fortawesome/fontawesome-free/css/all.min.css';
import './App.css';
import Logo from './Logo';

const API_KEY = 'f05354e7d53403784e9dc008e4fe9c02';
const BASE_URL = 'https://api.openweathermap.org/data/2.5';

const weatherIcons = {
  'Clear': 'fa-sun',
  'Clouds': 'fa-cloud',
  'Rain': 'fa-cloud-rain',
  'Drizzle': 'fa-cloud-rain',
  'Thunderstorm': 'fa-bolt',
  'Snow': 'fa-snowflake',
  'Mist': 'fa-smog',
  'Smoke': 'fa-smog',
  'Haze': 'fa-smog',
  'Dust': 'fa-smog',
  'Fog': 'fa-smog',
  'Sand': 'fa-smog',
  'Ash': 'fa-smog',
  'Squall': 'fa-wind',
  'Tornado': 'fa-tornado'
};

function formatTime(timestamp, timezone) {
  // timestamp: seconds, timezone: seconds offset
  const date = new Date((timestamp + timezone) * 1000);
  return date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });
}

function WeatherApp() {
  const [city, setCity] = useState('Istanbul');
  const [weatherData, setWeatherData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedDay, setSelectedDay] = useState(null);

  const formatDate = (timestamp) => {
    const date = new Date(timestamp * 1000);
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    return date.toLocaleDateString('tr-TR', options);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      getWeatherData();
    }
  };

  const getWeatherData = async () => {
    if (!city.trim()) {
      setError('Lütfen bir şehir adı girin');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const currentWeatherResponse = await axios.get(
        `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${API_KEY}&units=metric&lang=tr`
      );

      const forecastResponse = await axios.get(
        `https://api.openweathermap.org/data/2.5/forecast?q=${city}&appid=${API_KEY}&units=metric&lang=tr`
      );

      setWeatherData({
        current: currentWeatherResponse.data,
        forecast: forecastResponse.data
      });
      setSelectedDay(null);
    } catch (err) {
      setError('Şehir bulunamadı veya bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getWeatherData();
  }, []);

  const handleForecastClick = (day) => {
    setSelectedDay(day);
    // Smooth scroll to hourly forecast
    setTimeout(() => {
      const hourlySection = document.getElementById('hourly-forecast-section');
      if (hourlySection) {
        const yOffset = -20; // Offset to account for any fixed headers
        const y = hourlySection.getBoundingClientRect().top + window.pageYOffset + yOffset;
        window.scrollTo({
          top: y,
          behavior: 'smooth'
        });
      }
    }, 100); // Small delay to ensure the DOM has updated
  };

  // Seçili günün saatlik verilerini filtrele
  const getSelectedDayHourly = () => {
    if (!selectedDay || !weatherData) return [];
    return weatherData.forecast.list.filter(item => {
      const itemDate = new Date(item.dt * 1000);
      const selectedDate = new Date(selectedDay * 1000);
      return (
        itemDate.getDate() === selectedDate.getDate() &&
        itemDate.getMonth() === selectedDate.getMonth() &&
        itemDate.getFullYear() === selectedDate.getFullYear()
      );
    });
  };

  return (
    <div className="app">
      <div className="container">
        <div className="app-header">
          <Logo size={50} />
          <h1 className="app-title">TasoWeather™</h1>
        </div>
        <div className="search-box">
          <input
            type="text"
            placeholder="Şehir ara..."
            value={city}
            onChange={(e) => setCity(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <button onClick={getWeatherData}>
            <i className="fas fa-search"></i>
          </button>
        </div>

        <AnimatePresence mode="wait">
          {loading ? (
            <motion.div
              key="loading"
              className="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <i className="fas fa-spinner fa-spin"></i> Yükleniyor...
            </motion.div>
          ) : error ? (
            <motion.div
              key="error"
              className="error"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <i className="fas fa-exclamation-circle"></i> {error}
            </motion.div>
          ) : weatherData && (
            <motion.div
              key="weather"
              className="weather-content"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="weather-box">
                <div className="weather-info">
                  <h1 className="city">{weatherData.current.name}</h1>
                  <div className="date">{formatDate(weatherData.current.dt)}</div>
                  <div className="temperature">
                    <span className="temp">{Math.round(weatherData.current.main.temp)}</span>°C
                  </div>
                  <motion.div 
                    className="weather-icon"
                    animate={{ y: [0, -10, 0] }}
                    transition={{ duration: 3, repeat: Infinity }}
                  >
                    <img src={`http://openweathermap.org/img/wn/${weatherData.current.weather[0].icon}@2x.png`} alt={weatherData.current.weather[0].description} />
                  </motion.div>
                  <div className="description">{weatherData.current.weather[0].description}</div>
                </div>

                <div className="weather-details">
                  <div className="detail-item">
                    <i className="fas fa-tint"></i>
                    <div className="value">{weatherData.current.main.humidity}%</div>
                    <span>Nem</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-wind"></i>
                    <div className="value">{Math.round(weatherData.current.wind.speed * 3.6)} km/s</div>
                    <span>Rüzgar</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-temperature-high"></i>
                    <div className="value">{Math.round(weatherData.current.main.feels_like)}°C</div>
                    <span>Hissedilen</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-compress-alt"></i>
                    <div className="value">{weatherData.current.main.pressure} hPa</div>
                    <span>Basınç</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-eye"></i>
                    <div className="value">{(weatherData.current.visibility / 1000).toFixed(1)} km</div>
                    <span>Görüş</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-sun"></i>
                    <div className="value">{formatTime(weatherData.current.sys.sunrise, weatherData.current.timezone)}</div>
                    <span>Gün Doğumu</span>
                  </div>
                  <div className="detail-item">
                    <i className="fas fa-moon"></i>
                    <div className="value">{formatTime(weatherData.current.sys.sunset, weatherData.current.timezone)}</div>
                    <span>Gün Batımı</span>
                  </div>
                </div>
              </div>

              <div className="forecast-container">
                <h3>5 Günlük Tahmin</h3>
                <div className="forecast">
                  {weatherData.forecast.list
                    .filter(item => item.dt_txt.includes('12:00:00'))
                    .map((forecast, index) => (
                      <motion.div
                        key={index}
                        className={`forecast-item${selectedDay === forecast.dt ? ' selected' : ''}`}
                        whileHover={{ y: -5, scale: 1.04, boxShadow: '0 4px 16px #ffd60099' }}
                        transition={{ duration: 0.2 }}
                        onClick={() => handleForecastClick(forecast.dt)}
                        style={{ cursor: 'pointer' }}
                      >
                        <div className="day">
                          {new Date(forecast.dt * 1000).toLocaleDateString('tr-TR', { weekday: 'short' })}
                        </div>
                        <i className={`fas ${weatherIcons[forecast.weather[0].main] || 'fa-cloud'}`}></i>
                        <div className="temp">{Math.round(forecast.main.temp)}°C</div>
                        <div className="desc">{forecast.weather[0].description}</div>
                        <div className="minmax">
                          <span><i className="fas fa-arrow-up"></i> {Math.round(forecast.main.temp_max)}°</span>
                          <span><i className="fas fa-arrow-down"></i> {Math.round(forecast.main.temp_min)}°</span>
                        </div>
                      </motion.div>
                    ))}
                </div>
                {/* Saatlik tahmin */}
                {selectedDay && (
                  <div className="hourly-container" id="hourly-forecast-section">
                    <h4>Saatlik Tahmin</h4>
                    <div className="hourly-forecast">
                      {getSelectedDayHourly().map((h, idx) => (
                        <div className="hourly-item" key={idx}>
                          <div className="hour">{formatTime(h.dt, weatherData.current.timezone)}</div>
                          <i className={`fas ${weatherIcons[h.weather[0].main] || 'fa-cloud'}`}></i>
                          <div className="temp">{Math.round(h.main.temp)}°C</div>
                          <div className="desc">{h.weather[0].description}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      <footer className="footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>TasoWeather™</h3>
            <p>Modern ve kullanıcı dostu hava durumu uygulaması</p>
          </div>
          <div className="footer-section">
            <h3>Hızlı Bağlantılar</h3>
            <ul>
              <li><Link to="/">Ana Sayfa</Link></li>
              <li><a href="https://tahsinmertmutlucv.netlify.app/" target="_blank" rel="noopener noreferrer">Hakkımda</a></li>
            </ul>
          </div>
          <div className="footer-section">
            <h3>İletişim</h3>
            <ul>
              <li><a href="https://github.com/tahsinmert" target="_blank" rel="noopener noreferrer"><i className="fab fa-github"></i> GitHub</a></li>
            </ul>
          </div>
        </div>
        <div className="footer-bottom">
          <p>&copy; 2025 TasoWeather™. Tüm hakları saklıdır.</p>
          <p className="developer-credit">
            <i className="fas fa-code"></i> Coded with <i className="fas fa-heart"></i> by 
            <a href="https://github.com/tahsinmert" target="_blank" rel="noopener noreferrer">
              Tahsin Mert MUTLU
            </a>
          </p>
        </div>
      </footer>
    </div>
  );
}

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<WeatherApp />} />
      </Routes>
    </Router>
  );
}

export default App;
